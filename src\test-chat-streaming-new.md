# Test Chat Streaming System - New Implementation

## Tổng quan

Đã tạo lại hoàn toàn hệ thống chat streaming theo yêu cầu mới với luồng:

1. **Hiển thị tin nhắn user**
2. **Hiển thị loading agent**
3. **Call API POST /v1/chat/message**
4. **Connect SSE GET /v1/chat/stream/events/{threadId}/{runId}**
5. **Stream text từng từ**
6. **X<PERSON> lý interrupt với DELETE /v1/chat/runs/{runId}**

## Các File Đã Tạo

### 1. Services

#### `src/shared/services/chat-config.service.ts`
- Quản lý cấu hình chat (agent ID, API URLs, etc.)
- Singleton pattern với localStorage persistence
- Validation và default configuration

#### `src/shared/services/chat-api.service.ts`
- Xử lý REST API calls
- POST `/v1/chat/message` để gửi tin nhắn
- DELETE `/v1/chat/runs/{runId}` để dừng streaming
- Error handling và retry logic

#### `src/shared/services/chat-sse.service.ts`
- Quản lý SSE connection
- GET `/v1/chat/stream/events/{threadId}/{runId}`
- Xử lý events: `connected`, `stream_text_token`, `stream_tool_token`, `llm_stream_end`
- Auto-disconnect và error handling

### 2. Hook

#### `src/shared/hooks/common/useChatStream.ts`
- Hook chính quản lý toàn bộ logic
- Tích hợp tất cả services
- State management cho messages, streaming, errors
- Optimistic updates cho user messages

### 3. Updated Components

#### `src/shared/components/layout/chat-panel/ChatPanel.tsx`
- Cập nhật để sử dụng hook mới
- Xử lý error notifications
- Convert messages sang legacy format cho UI

#### `src/shared/components/layout/chat-panel/ChatInputBox.tsx`
- Cập nhật để sử dụng methods mới
- `stopStreaming()` thay vì `stopCurrentRun()`
- Loại bỏ `isLoading` property không tồn tại

## Luồng Chat Mới

### Gửi Tin Nhắn

```typescript
const sendMessage = async (content: string) => {
  // 1. Hiển thị tin nhắn user ngay lập tức
  const userMessage = {
    id: uuidv4(),
    content,
    sender: 'user',
    timestamp: new Date(),
    status: MessageStatus.SENT,
    threadId
  };
  setMessages(prev => [...prev, userMessage]);

  // 2. Tạo message rỗng cho AI với loading state
  const assistantMessage = {
    id: uuidv4(),
    content: '',
    sender: 'assistant',
    timestamp: new Date(),
    status: MessageStatus.STREAMING,
    threadId
  };
  setMessages(prev => [...prev, assistantMessage]);
  setIsStreaming(true);

  // 3. Dừng run trước đó nếu có
  if (currentRunId) {
    await apiService.stopRun(currentRunId);
    sseService.disconnect();
  }

  // 4. Call API gửi tin nhắn
  const response = await apiService.sendMessage(content, threadId, alwaysApproveToolCall);
  const newRunId = response.result.runId;
  setCurrentRunId(newRunId);

  // 5. Connect SSE để nhận stream
  await sseService.connect(threadId, newRunId);
};
```

### SSE Event Handling

```typescript
sseService.setCallbacks({
  onConnected: (data) => {
    console.log('SSE Connected:', data);
    setIsConnected(true);
  },
  
  onTextToken: (text: string, role: string) => {
    // Append text to current streaming message
    setCurrentStreamingText(prev => prev + text);
    
    // Update message content in real-time
    if (currentStreamingMessage) {
      setMessages(prev => prev.map(msg => 
        msg.id === currentStreamingMessage.id
          ? { ...msg, content: currentStreamingMessage.content + text }
          : msg
      ));
      currentStreamingMessage.content += text;
    }
  },
  
  onStreamEnd: (threadId: string, runId: string) => {
    // Finalize message
    setMessages(prev => prev.map(msg => 
      msg.id === currentStreamingMessage?.id
        ? { ...msg, status: MessageStatus.COMPLETED }
        : msg
    ));
    
    // Reset streaming state
    setIsStreaming(false);
    setCurrentStreamingText('');
    setCurrentRunId(null);
    setIsConnected(false);
  },
  
  onError: (error: Error) => {
    setError(error.message);
    setIsStreaming(false);
    setIsConnected(false);
  }
});
```

### Interrupt Streaming

```typescript
const stopStreaming = async () => {
  if (!currentRunId) return;
  
  // Call API để dừng run
  await apiService.stopRun(currentRunId);
  
  // Disconnect SSE
  sseService.disconnect();
  
  // Reset state
  setIsStreaming(false);
  setCurrentRunId(null);
  setIsConnected(false);
};
```

## API Format

### POST /v1/chat/message

**Request:**
```json
{
  "contentBlocks": [
    {
      "type": "text",
      "content": "Hello, how can I help you?"
    }
  ],
  "threadId": "dc02eb3b-7299-4712-ba6b-603fa29714e6",
  "alwaysApproveToolCall": false
}
```

**Response:**
```json
{
  "code": 201,
  "message": "Created Successfully",
  "result": {
    "runId": "36e705c0-5f3f-4ac7-ab27-6d59a1d97414",
    "agentId": "fdcdbf13-2682-436d-98ca-ff404b7b8537",
    "agentName": "Agent System 3",
    "status": "created",
    "createdAt": 1749369622431,
    "message": "Message sent successfully. Run created and queued for processing."
  }
}
```

### GET /v1/chat/stream/events/{threadId}/{runId}

**SSE Events:**
```
event: connected
data: {"threadId":"dc02eb3b-7299-4712-ba6b-603fa29714e6","from":"live","timestamp":1749369835112}

event: stream_text_token
data: {"event":"stream_text_token","data":{"role":"supervisor","text":"Hi"},"timestamp":1749369624220}

event: llm_stream_end
data: {"event":"llm_stream_end","data":{"threadId":"dc02eb3b-7299-4712-ba6b-603fa29714e6","runId":"36e705c0-5f3f-4ac7-ab27-6d59a1d97414"},"timestamp":1749369624477}
```

### DELETE /v1/chat/runs/{runId}

**Response:**
```json
{
  "code": 200,
  "message": "Run stopped successfully",
  "result": {
    "runId": "36e705c0-5f3f-4ac7-ab27-6d59a1d97414",
    "status": "stopped"
  }
}
```

## Cách Sử Dụng

```typescript
import { useChatStream } from '@/shared/hooks/common';
import { chatConfigService } from '@/shared/services';

const MyComponent = () => {
  const { getToken } = useAuthCommon();
  
  const chatStream = useChatStream({
    agentId: 'your-agent-id',
    getAuthToken: () => getToken() || '',
    debug: true
  });

  const handleSendMessage = async (content: string) => {
    await chatStream.sendMessage(content);
  };

  const handleStopStreaming = async () => {
    await chatStream.stopStreaming();
  };

  return (
    <div>
      {chatStream.messages.map(message => (
        <div key={message.id}>
          <strong>{message.sender}:</strong> {message.content}
          {message.status === 'streaming' && <span> (streaming...)</span>}
        </div>
      ))}
      
      {chatStream.isStreaming && (
        <div>Current text: {chatStream.currentStreamingText}</div>
      )}
      
      <button onClick={() => handleSendMessage('Hello')}>
        Send Message
      </button>
      
      {chatStream.isStreaming && (
        <button onClick={handleStopStreaming}>
          Stop Streaming
        </button>
      )}
      
      {chatStream.error && (
        <div style={{ color: 'red' }}>Error: {chatStream.error}</div>
      )}
    </div>
  );
};
```

## Tính Năng

✅ **Đã Triển Khai:**
- Luồng chat theo yêu cầu mới
- SSE connection management
- Text token streaming từng từ
- REST API integration
- Error handling
- Interrupt streaming
- Optimistic updates
- Configuration management
- Debug logging

🔄 **Có Thể Mở Rộng:**
- Multiple content types (images, files)
- Message persistence
- Multiple agent support
- Advanced error recovery
- Performance optimizations
- Unit tests

## Troubleshooting

1. **SSE Connection Failed:** Kiểm tra URL và auth token
2. **Messages không hiển thị:** Kiểm tra message format conversion
3. **API Calls Failed:** Verify endpoints và authentication
4. **Streaming không hoạt động:** Kiểm tra SSE event handlers

## Debug Mode

Enable debug mode để xem detailed logs:

```typescript
const chatStream = useChatStream({
  // ... other config
  debug: true
});
```

Hoặc cập nhật config:

```typescript
chatConfigService.setDebug(true);
```
