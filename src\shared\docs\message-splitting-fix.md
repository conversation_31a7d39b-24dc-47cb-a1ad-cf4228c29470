# Message Splitting & MessageId Assignment Fix

Tài liệu này mô tả các fix cho vấn đề message bị split và messageId assignment.

## ❌ Vấn đề đã phát hiện

### 1. Message bị tách thành 2 tin nhắn
- Message bị split mặc dù chưa có `llm_stream_end`
- Role change logic quá aggressive
- Minor role variations (assistant → supervisor) gây split

### 2. MessageId không được gắn đúng
- `message_created` event chỉ gán messageId cho role `assistant`
- <PERSON><PERSON><PERSON> roles khác như `supervisor` không được gán messageId
- Thiếu debug logs để track messageId assignment

## ✅ Giải pháp đã implement

### Fix 1: Smart Role Change Logic

**Vấn đề**: Role change logic tạo message mới cho mọi role change
```typescript
// ❌ Trước đây - split cho mọi role change
} else if (currentStreamingMessageRef.current.sender !== mappedSender) {
  // Always create new message
  const finalizedMessage = { ...currentStreamingMessageRef.current, status: MessageStatus.COMPLETED };
  const newMessage = { /* new message */ };
  // Split message
}
```

**Giải pháp**: Chỉ split cho significant role changes
```typescript
// ✅ Sau khi sửa - smart splitting
} else if (currentStreamingMessageRef.current.sender !== mappedSender) {
  // ✅ ONLY split if it's a significant role change (e.g., user -> assistant, assistant -> user)
  // Don't split for minor role variations within AI responses (assistant -> supervisor)
  const shouldSplitMessage = (
    (currentStreamingMessageRef.current.sender === 'user' && mappedSender === 'assistant') ||
    (currentStreamingMessageRef.current.sender === 'assistant' && mappedSender === 'user')
  );

  if (shouldSplitMessage) {
    // Split message
  } else {
    // Continue with same message, just append text
    currentStreamingMessageRef.current.content += text;
  }
}
```

### Fix 2: Universal MessageId Assignment

**Vấn đề**: Chỉ gán messageId cho role `assistant`
```typescript
// ❌ Trước đây - chỉ handle assistant
if (role === 'assistant' && currentStreamingMessageRef.current) {
  // Assign messageId
}
```

**Giải pháp**: Gán messageId cho tất cả roles
```typescript
// ✅ Sau khi sửa - handle tất cả roles
console.log('[useChatStream] 🆔 MESSAGE CREATED event:', {
  messageId,
  role,
  contentPreview: contentPreview?.substring(0, 50) + '...',
  hasCurrentMessage: !!currentStreamingMessageRef.current,
  currentMessageId: currentStreamingMessageRef.current?.id
});

// Gán messageId cho message hiện tại (bất kể role nào)
if (currentStreamingMessageRef.current) {
  const updatedMessage: ChatMessage = {
    ...currentStreamingMessageRef.current,
    messageId: messageId, // Gán messageId từ API
    metadata: {
      ...currentStreamingMessageRef.current.metadata,
      apiMessageId: messageId,
      contentPreview: contentPreview,
      messageCreatedRole: role // Track role từ message_created event
    }
  };
  // Update message
}
```

### Fix 3: Enhanced Debug Logging

**Vấn đề**: Không có debug info cho role changes và messageId assignment

**Giải pháp**: Comprehensive debug logging
```typescript
// Role change detection
console.warn('[useChatStream] 🔄 ROLE CHANGE DETECTED:', {
  currentSender: currentStreamingMessageRef.current.sender,
  newSender: mappedSender,
  currentRole: currentRole,
  newRole: role,
  text: text.substring(0, 50) + '...'
});

// MessageId assignment
console.log('[useChatStream] 🆔 MESSAGE CREATED event:', {
  messageId,
  role,
  contentPreview: contentPreview?.substring(0, 50) + '...',
  hasCurrentMessage: !!currentStreamingMessageRef.current
});

console.log('[useChatStream] ✅ MessageId assigned to current message:', messageId);
```

## 🔍 Logic Flow

### Smart Role Change Decision Tree

```
Role Change Detected?
├── Yes
│   ├── Significant Change? (user ↔ assistant)
│   │   ├── Yes → Split Message
│   │   └── No → Continue Same Message
│   └── No → Continue Same Message
└── No → Continue Same Message
```

### Significant vs Minor Role Changes

| Change Type | Example | Action |
|-------------|---------|--------|
| **Significant** | user → assistant | Split message |
| **Significant** | assistant → user | Split message |
| **Minor** | assistant → supervisor | Continue same message |
| **Minor** | supervisor → assistant | Continue same message |
| **Minor** | assistant → worker | Continue same message |

### MessageId Assignment Flow

```
message_created Event Received
├── Has Current Streaming Message?
│   ├── Yes
│   │   ├── Assign messageId to current message
│   │   ├── Update metadata with apiMessageId
│   │   ├── Track messageCreatedRole
│   │   └── Log success
│   └── No
│       └── Log warning (no message to assign to)
└── Continue
```

## 🎯 Expected Results

### 1. No Unnecessary Message Splitting
- AI responses remain as single continuous messages
- Only split when switching between user and AI
- Smooth streaming experience

### 2. Proper MessageId Assignment
- All streaming messages get messageId from `message_created` event
- Works for all roles: assistant, supervisor, worker, etc.
- Metadata includes both `messageId` and `apiMessageId`

### 3. Better Debug Visibility
- Clear logs for role change decisions
- MessageId assignment tracking
- Easy troubleshooting

## 🔧 Debug Commands

### Check Role Change Logic
```javascript
// Monitor role changes in console
// Look for: "🔄 ROLE CHANGE DETECTED"
// Check if shouldSplitMessage = true/false
```

### Check MessageId Assignment
```javascript
// Monitor messageId assignment
// Look for: "🆔 MESSAGE CREATED event"
// Look for: "✅ MessageId assigned to current message"
```

### Check Message Continuity
```javascript
// Check if messages are being split unnecessarily
// Count number of AI messages in a single response
// Should be 1 for most cases
```

## 🚨 Troubleshooting

### Issue 1: Messages still splitting
- Check console for "🔄 ROLE CHANGE DETECTED"
- Verify `shouldSplitMessage` logic
- Check if roles are being mapped correctly

### Issue 2: MessageId not assigned
- Check console for "🆔 MESSAGE CREATED event"
- Verify `currentStreamingMessageRef.current` exists
- Check if `message_created` event is being received

### Issue 3: Wrong messageId assignment
- Check `messageCreatedRole` in metadata
- Verify timing of `message_created` vs streaming tokens
- Check if multiple messages are receiving same messageId

## 🎯 Testing

### Test Cases
1. **Single AI Response**: Should remain as 1 message
2. **Multi-role Response**: assistant + supervisor should be 1 message
3. **User-AI Conversation**: Should split between user and AI
4. **MessageId Assignment**: All AI messages should have messageId

### Expected Behavior
- Smooth single-message streaming
- Proper messageId assignment for all roles
- Clear debug logs for troubleshooting
- No unnecessary message splitting

Với những fixes này, streaming sẽ hoạt động smooth và messageId sẽ được gán đúng cách cho tất cả messages!
