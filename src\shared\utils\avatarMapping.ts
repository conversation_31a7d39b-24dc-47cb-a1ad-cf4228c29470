/**
 * Avatar Mapping Utility
 * Quản lý avatar cho các role khác nhau trong chat system
 */

export type ChatRole = 'user' | 'assistant' | 'supervisor' | 'worker';

export interface AvatarConfig {
  url: string;
  alt: string;
  name: string;
  color?: string;
}

/**
 * Mapping avatar cho từng role
 */
export const ROLE_AVATAR_MAPPING: Record<ChatRole, AvatarConfig> = {
  user: {
    url: '/assets/images/default-avatar.svg',
    alt: 'User Avatar',
    name: 'User',
    color: '#3B82F6' // Blue
  },
  assistant: {
    url: '/assets/images/ai-agents/assistant-robot.svg',
    alt: 'AI Assistant Avatar',
    name: 'AI Assistant',
    color: '#FF3333' // Red
  },
  supervisor: {
    url: '/assets/images/ai-agents/researcher-robot.svg',
    alt: 'Supervisor Avatar',
    name: 'Supervisor',
    color: '#10B981' // Green
  },
  worker: {
    url: '/assets/images/ai-agents/coder-robot.svg',
    alt: 'Worker Avatar',
    name: 'Worker',
    color: '#3366FF' // Blue
  }
};

/**
 * Lấy avatar config cho role
 * @param role - Chat role
 * @param customAvatar - Custom avatar URL (for supervisor using agent avatar)
 */
export const getAvatarForRole = (role: ChatRole, customAvatar?: string): AvatarConfig => {
  const config = ROLE_AVATAR_MAPPING[role] || ROLE_AVATAR_MAPPING.assistant;

  // Sử dụng custom avatar nếu được cung cấp (chủ yếu cho supervisor)
  if (customAvatar && role === 'supervisor') {
    return {
      ...config,
      url: customAvatar
    };
  }

  return config;
};

/**
 * Lấy avatar URL cho role
 */
export const getAvatarUrlForRole = (role: ChatRole): string => {
  return getAvatarForRole(role).url;
};

/**
 * Lấy tên hiển thị cho role
 */
export const getRoleDisplayName = (role: ChatRole): string => {
  const roleNames: Record<ChatRole, string> = {
    user: 'Người dùng',
    assistant: 'AI Assistant',
    supervisor: 'Supervisor',
    worker: 'Worker'
  };

  return roleNames[role] || roleNames.assistant;
};

/**
 * Kiểm tra role có cần hiển thị avatar không
 */
export const shouldShowAvatarForRole = (role: ChatRole): boolean => {
  return role !== 'user';
};

/**
 * Kiểm tra role có cần hiển thị box không
 */
export const shouldShowBoxForRole = (role: ChatRole): boolean => {
  return role === 'user';
};

/**
 * Kiểm tra role có align bên phải không
 */
export const isRightAlignedRole = (role: ChatRole): boolean => {
  return role === 'user';
};

/**
 * Kiểm tra role có cần hiển thị tên role không
 */
export const shouldShowRoleName = (role: ChatRole): boolean => {
  return role === 'worker'; // Chỉ hiển thị cho worker, ẩn supervisor
};

/**
 * Lấy màu chủ đề cho role
 */
export const getRoleColor = (role: ChatRole): string => {
  return getAvatarForRole(role).color || '#6B7280';
};

/**
 * Lấy class CSS cho role
 */
export const getRoleClasses = (role: ChatRole): {
  container: string;
  avatar: string;
  content: string;
  roleName: string;
} => {
  const isRightAligned = isRightAlignedRole(role);
  
  return {
    container: `flex ${isRightAligned ? 'justify-end' : 'justify-start'} items-start`,
    avatar: `w-8 h-8 rounded-full overflow-hidden ${isRightAligned ? 'ml-2' : 'mr-2'}`,
    content: `max-w-[80%] min-w-0`, // Removed flex-1 to work with new layout
    roleName: `text-xs text-gray-500 dark:text-gray-400 mb-1 ${isRightAligned ? 'text-right' : 'text-left'}`
  };
};
